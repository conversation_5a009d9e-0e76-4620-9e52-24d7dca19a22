<{capture name="header"}>
<{script src="coms/modedialog.js" app="desktop"}>
<{script src="coms/pager.js" app="desktop"}>
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<{/capture}>

<style>
    /* 重置和基础样式 */
    * {
        box-sizing: border-box;
    }

    html {
        width: 100%;
        overflow-x: hidden;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        font-size: 12px;
        line-height: 1.4;
        background: #f8f9fa;
        width: 100%;
        overflow-x: hidden;
        min-width: 320px; /* 最小宽度防止过度压缩 */
    }

    .repair-container * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    /* 主容器样式 */
    .repair-container {
        max-width: 100%;
        width: 100%;
        margin: 0;
        padding: 1rem;
        background: #f8f9fa;
        min-height: calc(100vh - 100px);
        font-family: Arial, Helvetica, sans-serif, "宋体";
        font-size: 12px;
        overflow-x: hidden;
        position: relative;
    }

    /* 标题样式 */
    .repair-header {
        text-align: center;
        margin-bottom: 2rem;
        color: #4f638f;
        font-size: 18px;
        font-weight: bold;
        padding: 1rem 0;
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .repair-header::after {
        content: '';
        display: block;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, #3367AC, #4f638f);
        margin: 0.5rem auto 0;
        border-radius: 2px;
    }

    /* 功能列表样式 */
    .function-list {
        display: block;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .function-item {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        padding: 12px 15px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 8px;
        float: left;
        width: 48%;
        margin-right: 2%;
        min-height: 80px;
    }

    .function-item:nth-child(2n) {
        margin-right: 0;
    }

    .function-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #3367AC;
        transform: scaleX(0);
        transition: transform 0.2s ease;
    }

    .function-item:hover {
        border-color: #3367AC;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(51, 103, 172, 0.15);
        background: #f8faff;
    }

    .function-item:hover::before {
        transform: scaleX(1);
    }

    .function-item.active {
        background: #3367AC;
        color: white;
        border-color: #3367AC;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(51, 103, 172, 0.3);
    }

    .function-item.active::before {
        background: rgba(255, 255, 255, 0.3);
        transform: scaleX(1);
    }

    .function-item h3 {
        margin: 0 0 6px 0;
        color: #4f638f;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.4;
        display: block;
    }

    .function-item.active h3 {
        color: white;
    }

    .function-item p {
        margin: 0;
        color: #666;
        font-size: 12px;
        line-height: 1.4;
    }

    .function-item .function-description {
        margin: 6px 0 0 0;
        color: #888;
        font-size: 11px;
        line-height: 1.3;
        font-style: italic;
    }

    .function-item.active p {
        color: rgba(255, 255, 255, 0.9);
    }

    .function-item::after {
        content: '→';
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #3367AC;
        font-size: 14px;
        opacity: 0;
        transition: all 0.2s ease;
    }

    .function-item:hover::after,
    .function-item.active::after {
        opacity: 1;
        transform: translateY(-50%) translateX(2px);
    }

    .function-item.active::after {
        color: white;
    }

    /* 操作区域样式 */
    .operation-area {
        display: none;
        margin-top: 1.5rem;
        clear: both;
    }

    .operation-area.active {
        display: block;
    }

    @media (min-width: 768px) {
        .operation-area.active {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .repair-form-container,
        .response-container {
            display: table-cell;
            vertical-align: top;
        }

        .repair-form-container {
            width: 60%;
            padding-right: 15px;
        }

        .response-container {
            width: 40%;
            padding-left: 15px;
        }
    }

    .repair-form-container {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
    }

    .repair-form-container h3 {
        color: #4f638f;
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        font-weight: bold;
        display: block;
    }

    .repair-form {
        display: block;
        background: #f9fafb;
        padding: 12px;
        border-radius: 4px;
        margin-top: 12px;
        border: 1px solid #e5e7eb;
    }

    .repair-form p {
        margin: 0 0 6px 0;
        color: #333;
        font-size: 12px;
        line-height: 1.4;
        font-weight: normal;
    }

    .repair-form input[type="text"],
    .repair-form textarea,
    .repair-form select {
        padding: 6px 8px;
        border: 1px solid #d1d5db;
        border-radius: 3px;
        font-size: 12px;
        transition: all 0.2s ease;
        width: 100%;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        background: #ffffff;
        line-height: 1.4;
        margin-bottom: 8px;
    }

    .repair-form input[type="text"]:focus,
    .repair-form textarea:focus,
    .repair-form select:focus {
        border-color: #3367AC;
        outline: none;
        box-shadow: 0 0 0 2px rgba(51, 103, 172, 0.1);
        background: #ffffff;
    }

    .repair-form textarea {
        resize: vertical;
        min-height: 60px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .repair-form select {
        cursor: pointer;
        background: #ffffff;
        padding-right: 20px;
    }

    .repair-submit {
        background: #3367AC;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        font-weight: normal;
        transition: all 0.2s ease;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        position: relative;
        margin-top: 8px;
        min-width: 80px;
        line-height: 1.4;
    }

    .repair-submit:hover {
        background: #2856a0;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(51, 103, 172, 0.2);
    }

    .repair-submit:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .repair-submit:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }

    .response-container {
        background: #fff;
        border: 1px solid #d5dfe3;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        padding: 15px;
        margin-bottom: 15px;
    }

    .response-container h3 {
        color: #4f638f;
        margin: 0 0 12px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        font-weight: bold;
        display: block;
    }

    .response-html {
        padding: 12px;
        background: #f9fafb;
        border-radius: 4px;
        min-height: 150px;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e5e7eb;
        font-size: 12px;
        line-height: 1.5;
        font-family: Arial, Helvetica, sans-serif, "宋体";
        word-break: break-word;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .loading-content {
        background: #fff;
        padding: 20px;
        border-radius: 4px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 1px solid #d5dfe3;
    }

    .loading-spinner {
        width: 30px;
        height: 30px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #3367AC;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 12px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #333;
        font-size: 12px;
        font-weight: normal;
        margin: 0;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .success-message {
        color: #166534;
        padding: 8px 12px;
        background: #f0fdf4;
        border-radius: 4px;
        margin: 8px 0;
        border-left: 3px solid #22c55e;
        font-size: 12px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .error-message {
        color: #991b1b;
        padding: 8px 12px;
        background: #fef2f2;
        border-radius: 4px;
        margin: 8px 0;
        border-left: 3px solid #ef4444;
        font-size: 12px;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .back-button {
        background: #6b7280;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        font-weight: normal;
        margin-bottom: 12px;
        transition: all 0.2s ease;
        font-family: Arial, Helvetica, sans-serif, "宋体";
    }

    .back-button:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }

    /* 响应式布局优化 */

    /* 超小屏幕 (小于576px) */
    @media (max-width: 575px) {
        html, body {
            overflow-x: hidden;
            width: 100%;
            max-width: 100vw;
        }

        .repair-container {
            padding: 4px;
            margin: 0;
            overflow-x: hidden;
            width: 100%;
            max-width: 100vw;
            box-sizing: border-box;
        }

        .repair-header {
            font-size: 14px;
            margin-bottom: 8px;
            padding: 0.5rem 0.25rem;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .function-item {
            width: 100%;
            margin-right: 0;
            margin-bottom: 4px;
            float: none;
            min-height: 50px;
            padding: 8px 10px;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .function-item h3 {
            font-size: 12px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .function-item p {
            font-size: 10px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .operation-area.active {
            display: block;
            width: 100%;
            overflow-x: hidden;
        }

        .repair-form-container,
        .response-container {
            display: block;
            width: 100%;
            padding: 6px;
            margin-bottom: 8px;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .repair-form {
            padding: 6px;
            overflow-x: hidden;
        }

        .repair-form input[type="text"],
        .repair-form textarea,
        .repair-form select {
            font-size: 16px; /* 避免iOS缩放 */
            padding: 6px;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .repair-submit {
            width: 100%;
            padding: 8px 12px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .back-button {
            width: 100%;
            font-size: 14px;
            padding: 8px 12px;
            box-sizing: border-box;
        }

        .loading-content {
            padding: 10px;
            margin: 6px;
            width: 90%;
            max-width: 280px;
            box-sizing: border-box;
        }

        .response-html {
            max-height: 160px;
            font-size: 11px;
            overflow-x: hidden;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    }

    /* 小屏幕 (576px - 767px) */
    @media (min-width: 576px) and (max-width: 767px) {
        html, body {
            overflow-x: hidden;
            width: 100%;
        }

        .repair-container {
            padding: 8px;
            overflow-x: hidden;
            width: 100%;
            max-width: 100vw;
            box-sizing: border-box;
        }

        .repair-header {
            font-size: 16px;
            margin-bottom: 12px;
            padding: 0.75rem 0.5rem;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .function-item {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
            float: none;
            min-height: 60px;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .operation-area.active {
            display: block;
            width: 100%;
            overflow-x: hidden;
        }

        .repair-form-container,
        .response-container {
            display: block;
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .repair-form {
            padding: 10px;
            overflow-x: hidden;
        }

        .repair-form input[type="text"],
        .repair-form textarea,
        .repair-form select {
            font-size: 16px; /* 避免iOS缩放 */
            padding: 8px;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .repair-submit {
            width: 100%;
            padding: 10px 16px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .back-button {
            width: 100%;
            font-size: 14px;
            box-sizing: border-box;
        }

        .loading-content {
            padding: 15px;
            margin: 10px;
            width: 85%;
            max-width: 320px;
            box-sizing: border-box;
        }

        .response-html {
            max-height: 200px;
            overflow-x: hidden;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    }

    /* 中等屏幕 (768px - 991px) - 平板竖屏 */
    @media (min-width: 768px) and (max-width: 991px) {
        .repair-container {
            padding: 15px;
            max-width: 100%;
        }

        .repair-header {
            font-size: 20px;
            margin-bottom: 20px;
        }

        .function-item {
            width: 48%;
            margin-right: 2%;
            margin-bottom: 10px;
            min-height: 85px;
        }

        .function-item:nth-child(2n) {
            margin-right: 0;
        }

        .operation-area.active {
            display: block;
            width: 100%;
        }

        .repair-form-container {
            width: 100%;
            margin-bottom: 20px;
            padding: 0;
        }

        .response-container {
            width: 100%;
            padding: 0;
        }

        .repair-form input[type="text"],
        .repair-form textarea,
        .repair-form select {
            font-size: 14px;
        }

        .repair-submit {
            width: auto;
            min-width: 120px;
            padding: 10px 20px;
        }

        .back-button {
            width: auto;
            min-width: 100px;
        }
    }

    /* 大屏幕 (992px - 1199px) - 平板横屏/小桌面 */
    @media (min-width: 992px) and (max-width: 1199px) {
        .repair-container {
            padding: 20px;
            max-width: 100%;
        }

        .repair-header {
            font-size: 22px;
            margin-bottom: 25px;
        }

        .function-item {
            width: 48%;
            margin-right: 2%;
            margin-bottom: 12px;
            min-height: 90px;
        }

        .function-item:nth-child(2n) {
            margin-right: 0;
        }

        .operation-area.active {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .repair-form-container {
            display: table-cell;
            vertical-align: top;
            width: 55%;
            padding-right: 20px;
        }

        .response-container {
            display: table-cell;
            vertical-align: top;
            width: 45%;
            padding-left: 20px;
        }

        .response-html {
            max-height: 350px;
        }
    }

    /* 超大屏幕 (1200px+) - 桌面 */
    @media (min-width: 1200px) {
        .repair-container {
            padding: 25px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .repair-header {
            font-size: 24px;
            margin-bottom: 30px;
        }

        .function-item {
            width: 31.33%;
            margin-right: 2%;
            margin-bottom: 15px;
            min-height: 95px;
        }

        .function-item:nth-child(3n) {
            margin-right: 0;
        }

        .function-item:nth-child(2n) {
            margin-right: 2%;
        }

        .operation-area.active {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .repair-form-container {
            display: table-cell;
            vertical-align: top;
            width: 60%;
            padding-right: 25px;
        }

        .response-container {
            display: table-cell;
            vertical-align: top;
            width: 40%;
            padding-left: 25px;
        }

        .response-html {
            max-height: 400px;
        }

        .loading-content {
            padding: 25px;
            max-width: 350px;
        }
    }

    /* 超宽屏幕 (1400px+) */
    @media (min-width: 1400px) {
        .repair-container {
            max-width: 1600px;
            padding: 30px;
        }

        .function-item {
            width: 23%;
            margin-right: 2.67%;
        }

        .function-item:nth-child(4n) {
            margin-right: 0;
        }

        .function-item:nth-child(3n) {
            margin-right: 2.67%;
        }

        .function-item:nth-child(2n) {
            margin-right: 2.67%;
        }
    }

    /* 清除浮动 */
    .function-list::after {
        content: "";
        display: table;
        clear: both;
    }

    /* 触摸设备优化 */
    @media (hover: none) and (pointer: coarse) {
        .function-item {
            padding: 15px;
            min-height: 70px;
        }

        .function-item:hover {
            transform: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .repair-submit,
        .back-button {
            padding: 12px 20px;
            font-size: 16px;
            min-height: 44px; /* iOS推荐的最小触摸目标 */
        }

        .repair-form input[type="text"],
        .repair-form textarea,
        .repair-form select {
            min-height: 44px;
            font-size: 16px;
        }
    }

    /* 高分辨率屏幕优化 */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .repair-header::after {
            height: 4px;
        }

        .function-item::before {
            height: 4px;
        }

        .loading-spinner {
            border-width: 4px;
        }
    }

    /* 横屏模式优化 */
    @media (orientation: landscape) and (max-height: 500px) {
        .repair-container {
            padding: 10px;
        }

        .repair-header {
            font-size: 16px;
            margin-bottom: 15px;
            padding: 0.5rem 0;
        }

        .function-item {
            min-height: 60px;
            padding: 10px 12px;
        }

        .response-html {
            max-height: 150px;
        }

        .loading-content {
            padding: 15px;
            max-width: 300px;
        }
    }

    /* 打印样式 */
    @media print {
        .repair-container {
            background: white;
            box-shadow: none;
            padding: 0;
        }

        .function-item {
            break-inside: avoid;
            box-shadow: none;
            border: 1px solid #ccc;
        }

        .loading-overlay,
        .back-button {
            display: none !important;
        }

        .operation-area {
            display: block !important;
        }

        .repair-form-container,
        .response-container {
            display: block !important;
            width: 100% !important;
            padding: 0 !important;
        }
    }

    /* 减少动画的用户偏好 */
    @media (prefers-reduced-motion: reduce) {
        .function-item,
        .repair-submit,
        .back-button {
            transition: none;
        }

        .function-item:hover {
            transform: none;
        }

        .loading-spinner {
            animation: none;
        }
    }

    /* 深色模式支持 */
    @media (prefers-color-scheme: dark) {
        body {
            background: #1a1a1a;
            color: #e0e0e0;
        }

        .repair-container {
            background: #1a1a1a;
        }

        .repair-header,
        .function-item,
        .repair-form-container,
        .response-container {
            background: #2d2d2d;
            border-color: #404040;
            color: #e0e0e0;
        }

        .repair-header {
            color: #7db3f0;
        }

        .function-item h3 {
            color: #7db3f0;
        }

        .function-item p {
            color: #b0b0b0;
        }

        .repair-form {
            background: #333;
            border-color: #404040;
        }

        .repair-form input[type="text"],
        .repair-form textarea,
        .repair-form select {
            background: #2d2d2d;
            border-color: #404040;
            color: #e0e0e0;
        }

        .repair-form input[type="text"]:focus,
        .repair-form textarea:focus,
        .repair-form select:focus {
            border-color: #7db3f0;
            box-shadow: 0 0 0 2px rgba(125, 179, 240, 0.2);
        }

        .response-html {
            background: #333;
            border-color: #404040;
            color: #e0e0e0;
        }

        .loading-content {
            background: #2d2d2d;
            border-color: #404040;
            color: #e0e0e0;
        }

        .loading-spinner {
            border-color: #404040;
            border-top-color: #7db3f0;
        }
    }
</style>

<div class="repair-container">
    <h1 class="repair-header">IT运维工具</h1>

    <!-- 功能选择列表 -->
    <div class="function-list" id="function-list">
        <{if $html}>
        <{foreach from=$html key=key item=value}>
        <div class="function-item" data-function="<{$key}>">
            <h3><{$value.desc}></h3>
            <p class="function-description" data-function-key="<{$key}>">点击选择此功能进行操作</p>
        </div>
        <{/foreach}>
        <{/if}>
    </div>

    <!-- 操作区域 -->
    <div class="operation-area" id="operation-area">
        <div class="repair-form-container">
            <button class="back-button" onclick="backToFunctionList()">返回功能列表</button>
            <h3 id="current-function-title">功能操作</h3>
            <div class="repair-form" id="repair-form" data-url="<{$process_url}>">
                <!-- 动态加载的表单内容 -->
            </div>
        </div>

        <div class="response-container">
            <h3>执行结果</h3>
            <div class="response-html" id="pair_right_response_html">
                <p style="color: #666; text-align: center; padding: 2rem;">请选择功能并执行操作，结果将在此处显示</p>
            </div>
        </div>
    </div>
</div>

<div class="loading-overlay" id="cover_float">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-text">处理中，请稍候...</div>
    </div>
</div>

<!-- 隐藏的表单模板 -->
<div style="display: none;">
    <{if $html}>
    <{foreach from=$html key=key item=value}>
    <div id="form-template-<{$key}>">
        <{$value.html}>
        <button class="repair-submit" data-sub-type="pair_submit_item">提交</button>
    </div>
    <{/foreach}>
    <{/if}>
</div>

<!-- 功能数据配置 -->
<script type="text/javascript">
    window.repairFunctionData = {
        <{if $html}>
        <{foreach from=$html key=key item=value name=func_loop}>
        '<{$key}>': {
            title: '<{$value.desc|escape:"javascript"}>',
            html: null
        }<{if !$smarty.foreach.func_loop.last}>,<{/if}>
        <{/foreach}>
        <{/if}>
    };
</script>

<script>
    // 全局变量
    let currentFunction = null;
    let functionData = window.repairFunctionData || {};

    // 初始化功能数据
    function initFunctionData() {
        // 获取表单HTML内容
        for (let key in functionData) {
            const templateEl = $('form-template-' + key);
            if (templateEl) {
                functionData[key].html = templateEl.get('html');

                // 提取功能描述并更新列表显示
                const htmlContent = templateEl.get('html');
                const description = extractFunctionDescription(htmlContent);
                if (description) {
                    const descEl = $$('.function-description[data-function-key="' + key + '"]')[0];
                    if (descEl) {
                        descEl.set('text', description);
                    }
                }
            }
        }
    }

    // 从HTML内容中提取功能描述
    function extractFunctionDescription(htmlContent) {
        // 查找包含功能说明的p标签
        const match = htmlContent.match(/<p[^>]*style="color:\s*#666[^"]*"[^>]*>(.*?)<\/p>/i);
        if (match && match[1]) {
            // 移除HTML标签，只保留文本
            return match[1].replace(/<[^>]*>/g, '').trim();
        }

        // 如果没找到特定格式的描述，尝试查找第一个p标签
        const generalMatch = htmlContent.match(/<p[^>]*>(.*?)<\/p>/i);
        if (generalMatch && generalMatch[1]) {
            const text = generalMatch[1].replace(/<[^>]*>/g, '').trim();
            // 过滤掉表单相关的文本
            if (!text.includes('：') && !text.includes('请输入') && !text.includes('请选择')) {
                return text;
            }
        }

        return null;
    }

    // 页面加载完成后初始化
    window.addEvent('domready', function() {
        initFunctionData();
        initFunctionSelection();
        initFormSubmission();
    });

    // 初始化功能选择
    function initFunctionSelection() {
        $$('.function-item').addEvent('click', function() {
            const functionKey = this.get('data-function');
            selectFunction(functionKey);
        });
    }

    // 选择功能
    function selectFunction(functionKey) {
        if (!functionData[functionKey]) return;

        currentFunction = functionKey;

        // 更新UI状态
        $$('.function-item').removeClass('active');
        $$('.function-item[data-function="' + functionKey + '"]').addClass('active');

        // 显示操作区域
        $('function-list').setStyle('display', 'none');
        $('operation-area').addClass('active');

        // 更新标题和表单内容
        $('current-function-title').set('text', functionData[functionKey].title);
        $('repair-form').set('html', functionData[functionKey].html);

        // 重新绑定提交事件
        initFormSubmission();

        // 清空结果区域
        responseEmpty();
    }

    // 返回功能列表
    function backToFunctionList() {
        $('function-list').setStyle('display', 'block');
        $('operation-area').removeClass('active');
        $$('.function-item').removeClass('active');
        currentFunction = null;
        responseEmpty();
    }

    // 初始化表单提交
    function initFormSubmission() {
        // 移除旧的事件监听器，避免重复绑定
        $$('.repair-submit').removeEvents('click');

        $$('.repair-submit').addEvent('click', function() {
            if (!currentFunction) return;

            const button = this;
            const form = button.getParent('.repair-form');
            const subUrl = form.get('data-url');
            const subDataString = form.toQueryString();
            const taskName = functionData[currentFunction].title;

            // 禁用提交按钮
            button.set('disabled', true);
            button.set('text', '处理中...');

            responseEmpty();

            const request = new Request({
                url: subUrl,
                method: 'post',
                data: subDataString,
                onRequest: function() {
                    $("cover_float").setStyle('display', 'flex');
                },
                onSuccess: function(responseText) {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle(responseText, taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                },
                onFailure: function() {
                    $("cover_float").setStyle('display', 'none');
                    responseHandle('{"code":500,"msg":"网络请求失败，请检查网络连接后重试"}', taskName);

                    // 恢复提交按钮
                    button.set('disabled', false);
                    button.set('text', '提交');
                }
            });

            request.send();
        });
    }

    // 处理响应结果
    function responseHandle(responseText, taskName) {
        const responseAreaHtml = $("pair_right_response_html");
        let json;

        try {
            json = JSON.decode(responseText);
        } catch (e) {
            json = {code: 500, msg: '响应数据格式错误'};
        }

        let html = '';
        const timestamp = new Date().toLocaleString();

        html += `<div style="border-bottom: 1px solid #e0e0e0; padding-bottom: 0.5rem; margin-bottom: 1rem;">`;
        html += `<h4 style="margin: 0; color: #2c3e50;">${taskName}</h4>`;
        html += `<small style="color: #666;">执行时间: ${timestamp}</small>`;
        html += `</div>`;

        if(json.code == 200) {
            html += `<div class="success-message">`;
            html += `<strong>✓ 执行成功</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else if(json.code == 500) {
            html += `<div class="error-message">`;
            html += `<strong>✗ 执行失败</strong><br>`;
            html += `${json.msg}`;
            html += `</div>`;
        } else {
            html += `<div class="error-message">`;
            html += `<strong>? 未知状态</strong><br>`;
            html += `响应代码: ${json.code}<br>`;
            html += `响应消息: ${json.msg || '无消息'}`;
            html += `</div>`;
        }

        responseAreaHtml.set('html', html);

        // 滚动到结果区域（移动端优化）
        if (window.innerWidth <= 767) {
            responseAreaHtml.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    // 清空响应区域
    function responseEmpty() {
        $("pair_right_response_html").set('html', '<p style="color: #666; text-align: center; padding: 2rem;">请执行操作，结果将在此处显示</p>');
    }

    // 键盘快捷键支持
    document.addEvent('keydown', function(e) {
        // ESC键返回功能列表
        if (e.key === 'Escape' && currentFunction) {
            backToFunctionList();
        }
    });

    // 移动端触摸优化
    if ('ontouchstart' in window) {
        $$('.function-item').addEvent('touchstart', function() {
            this.addClass('active');
        });

        $$('.function-item').addEvent('touchend', function() {
            // 延迟移除active状态，避免点击效果过快消失
            setTimeout(() => {
                if (this.get('data-function') !== currentFunction) {
                    this.removeClass('active');
                }
            }, 150);
        });
    }
    
    // 修复iOS设备上的点击延迟
    document.addEventListener('touchstart', function(){}, {passive: true});
</script>
